export const staticOptions = {
  RTW_LIST: [
    {
      name: 'Share code',
      key: 'share_code',
      isRequire: true,
      data: '',
      id: 1,
    },
    {
      name: 'Passport Front',
      key: 'passport_front',
      isRequire: true,
      data: '',
      id: 2,
    },
    {
      name: 'Passport Back',
      key: 'passport_back',
      isRequire: true,
      data: '',
      id: 3,
    },
    {
      name: 'CV',
      key: 'cv',
      isRequire: false,
      data: '',
      id: 4,
    },
    {
      name: 'BRP/E-Visa (JPG, PNG, PDF)',
      key: 'brp_front',
      isRequire: true,
      data: '',
      id: 5,
    },
    {
      name: 'BRP Back',
      key: 'brp_back',
      isRequire: true,
      data: '',
      id: 6,
    },
    {
      name: 'P45',
      Subname: 'If Applicable',
      key: 'p45',
      isRequire: false,
      data: '',
      id: 7,
    },
    {
      name: 'NI Letter',
      key: 'ni_letter',
      isRequire: true,
      data: '',
      id: 8,
    },
    {
      name: 'Students Letter',
      Subname: 'If Applicable',
      key: 'student_letter',
      isRequire: false,
      data: '',
      id: 9,
    },
    {
      name: 'Bank Statement/Utility Bill/Council Bill', //Bank Statements/DL/Utility Bill
      key: 'statements_dl_utility',
      isRequire: true,
      data: '',
      id: 10,
    },
  ],
  RTW_LIST_UK: [
    {
      name: 'Photo ID',
      Subname: 'Passport/Birth Certificate/Naturalization Certificate',
      key: 'photoID',
      isRequire: true,
      data: '',
      id: 1,
    },
    {
      name: 'NI Letter',
      Subname: 'Reference letter OR Previous pay slip',
      key: 'ni_letter',
      isRequire: true,
      data: '',
      id: 2,
    },
    {
      name: 'Current Address proof',
      Subname:
        'Utility Bill/Council Tax bill/Bank Statement, from the last 3 months',
      key: 'statements_dl_utility',
      isRequire: true,
      data: '',
      id: 3,
    },
    {
      name: 'P45 ',
      Subname: 'If Applicable',
      key: 'p45',
      isRequire: false,
      data: '',
      id: 4,
    },
    {
      name: 'CV',
      key: 'cv',
      isRequire: false,
      data: '',
      id: 5,
    },
  ],
  WORK_PERMIT_LIST: [
    {
      label: 'British National (Overseas) visa',
      value: 'British National (Overseas) Visa',
    },
    {
      label: 'Charity Worker visa (Temporary Work)',
      value: 'Charity Worker Visa (Temporary Work)',
    },
    {
      label: 'Creative Worker visa (Temporary Work)',
      value: 'Creative Worker Visa (Temporary Work)',
    },
    { label: 'Dependent Visa', value: 'Dependent Visa' },
    {
      label: 'Entrepreneur visa (Tier 1)',
      value: 'Entrepreneur Visa (Tier 1)',
    },
    { label: 'Exempt vignette', value: 'Exempt Vignette' },
    { label: 'Frontier Worker permit', value: 'Frontier Worker Permit' },
    { label: 'Global Talent visa', value: 'Global Talent Visa' },
    {
      label: 'Government Authorised Exchange visa (Temporary Work)',
      value: 'Government Authorised Exchange Visa (Temporary Work)',
    },
    {
      label: 'Graduate Trainee visa (Global Business Mobility)',
      value: 'Graduate Trainee Visa (Global Business Mobility)',
    },
    { label: 'Graduate visa', value: 'Graduate Visa' },
    {
      label: 'Health and Care Worker visa',
      value: 'Health and Care Worker Visa',
    },
    {
      label: 'High Potential Individual (HPI) visa',
      value: 'High Potential Individual (HPI) Visa',
    },
    {
      label: 'India Young Professionals Scheme visa',
      value: 'India Young Professionals Scheme Visa',
    },
    { label: 'Innovator Founder visa', value: 'Innovator Founder Visa' },
    {
      label: 'International Agreement visa (Temporary Work)',
      value: 'International Agreement Visa (Temporary Work)',
    },
    {
      label: 'International Sportsperson visa',
      value: 'International Sportsperson Visa',
    },
    { label: 'Investor visa (Tier 1)', value: 'Investor Visa (Tier 1)' },
    {
      label: 'Minister of Religion visa (T2)',
      value: 'Minister of Religion Visa (T2)',
    },
    { label: 'Other', value: 'Other' },
    { label: 'Over seas visa', value: 'Overseas Visa' },
    {
      label: 'Overseas Domestic Worker visa',
      value: 'Overseas Domestic Worker Visa',
    },
    {
      label: 'Religious Worker visa (Temporary Work)',
      value: 'Religious Worker Visa (Temporary Work)',
    },
    {
      label: 'Representative of an Overseas Business visa',
      value: 'Representative of an Overseas Business Visa',
    },
    { label: 'Scale-up Worker visa', value: 'Scale-up Worker Visa' },
    { label: 'Scale worker visa', value: 'Scale worker visa' },
    {
      label: 'Seasonal Worker visa (Temporary Work)',
      value: 'Seasonal Worker Visa (Temporary Work)',
    },
    {
      label: 'Secondment Worker visa (Global Business Mobility)',
      value: 'Secondment Worker Visa (Global Business Mobility)',
    },
    {
      label: 'Senior or Specialist Worker visa (Global Business Mobility)',
      value: 'Senior or Specialist Worker Visa (Global Business Mobility)',
    },
    {
      label: 'Service providers from Switzerland visa',
      value: 'Service Providers from Switzerland Visa',
    },
    {
      label: 'Service Supplier visa (Global Business Mobility)',
      value: 'Service Supplier Visa (Global Business Mobility)',
    },
    { label: 'Skilled Worker visa', value: 'Skilled Worker Visa' },
    { label: 'Start-up visa', value: 'Start-up Visa' },
    { label: 'Student', value: 'Student' },
    { label: 'Tire-1', value: 'Tier-1' },
    { label: 'Tire-2', value: 'Tier-2' },
    {
      label: 'Turkish Businessperson visa',
      value: 'Turkish Businessperson Visa',
    },
    { label: 'Turkish Worker visa', value: 'Turkish Worker Visa' },
    { label: 'UK Ancestry visa', value: 'UK Ancestry Visa' },
    {
      label: 'UK Expansion Worker visa (Global Business Mobility)',
      value: 'UK Expansion Worker Visa (Global Business Mobility)',
    },
    {
      label: 'Youth Mobility Scheme visa',
      value: 'Youth Mobility Scheme Visa',
    },
  ],
  CONTENT_TYPE: [
    { label: 'Document', value: 'document' },
    { label: 'Training', value: 'training' },
  ],
  DSR_CATEGORY_TYPE: [
    { label: 'Income', value: 'income' },
    { label: 'Other', value: 'other' },
    { label: 'Expense', value: 'expense' },
  ],
  DSR_SUB_CATEGORY_TYPE: [
    { label: 'Single', value: 'single' },
    { label: 'Multiple', value: 'multiple' },
  ],
  DSR_VALUE_TYPE: [
    { label: 'Number', value: 'number' },
    { label: 'String', value: 'string' },
  ],
  DSR_FIELD_STATUS: [
    { label: 'Active', value: 'active' },
    { label: 'In-Active', value: 'inactive' },
    { label: 'Delete', value: 'delete' },
  ],
  STATUS_OPTIONS: [
    { label: 'Rejected', value: 'rejected' },
    { label: 'Approved', value: 'approved' },
    { label: 'Pending', value: 'pending' },
    { label: 'Cancelled', value: 'cancelled' },
  ],
  DSR_STATUS_OPTIONS: [
    { label: 'Rejected', value: 'rejected' },
    { label: 'Approved', value: 'approved' },
    { label: 'Pending', value: 'pending' },
  ],
  STATUS: [
    { label: 'Active', value: 'active' },
    { label: 'Draft', value: 'draft' },
    { label: 'In-Active', value: 'inactive' },
  ],
  STATUSDELETED: [
    { label: 'Active', value: 'active' },
    { label: 'Draft', value: 'draft' },
    { label: 'In-Active', value: 'inactive' },
    { label: 'Deleted', value: 'delete' },
  ],
  CARD_STATUS: [
    { label: 'Active', value: 'active' },
    { label: 'In-Active', value: 'inactive' },
  ],
  LEAVE_OPTIONS: [
    { label: 'Casual', value: 'casual' },
    { label: 'Emergency', value: 'emergency' },
  ],
  LEAVE_TYPE: [
    { label: 'Hour', value: 'hour' },
    { label: 'Day', value: 'day' },
  ],
  USER_STATUS_OPTIONS: [
    { value: 'pending', label: 'Pending' },
    { value: 'active', label: 'Active' },
    { value: 'ongoing', label: 'Ongoing' },
    { value: 'completed', label: 'Completed' },
    { value: 'verified', label: 'Verified' },
    { value: 'deleted', label: 'Deleted' },
    { value: 'rejected', label: 'Rejected' },
    { value: 'cancelled', label: 'Cancelled' },
  ],
  RESIGNATION_OPTIONS: [
    { label: 'Pending', value: 'pending' },
    { label: 'In-discussion', value: 'in-discussion' },
    { label: 'Accepted', value: 'accepted' },
    { label: 'Rejected', value: 'rejected' },
    { label: 'Cancelled', value: 'cancelled' },
  ],
  RESIGNATION_OPTION: [
    { label: 'In-discussion', value: 'in-discussion' },
    { label: 'Accepted', value: 'accepted' },
    { label: 'Rejected', value: 'rejected' },
    { label: 'Cancelled', value: 'cancelled' },
  ],
  CHANGE_REQUEST_OPTION: [
    { label: 'Pending', value: 'pending' },
    // { label: 'Reopened', value: 'reopened' },
    { label: 'Approved', value: 'approved' },
    { label: 'Rejected', value: 'rejected' },
    { label: 'Cancelled', value: 'cancelled' },
    { label: 'Closed', value: 'closed' },
    { label: 'Deleted', value: 'deleted' },
  ],
  CHANGE_REQUEST_UPDATE_OPTION: [
    { label: 'Approved', value: 'approved' },
    { label: 'Rejected', value: 'rejected' },
    { label: 'Cancelled', value: 'cancelled' },
    { label: 'Closed', value: 'closed' },
  ],
  CHANGE_REQUEST_UPDATE_OPTIONS: [
    { label: 'Cancelled', value: 'cancelled' },
    { label: 'Closed', value: 'closed' },
  ],
  INVITE_STATUS: [
    { label: 'Invited', value: 'invited' },
    { label: 'Re-Invited', value: 'reinvited' },
    { label: 'Accepted', value: 'accepted' },
  ],
  CONTRACT_FILTER_STATUS: [
    { label: 'Confirmed', value: 'active' },
    { label: 'Probation', value: 'probation' },
    { label: 'Expired', value: 'expired' },
    { label: 'Expiry Soon', value: 'expiry-soon' },
    { label: 'Awaiting Signature', value: 'awaiting-signature' },
  ],
  TRAINING_FILTER_STATUS: [
    { label: 'Completed', value: 'completed' },
    { label: 'Pending', value: 'pending' },
    { label: 'Ongoing', value: 'ongoing' },
  ],
  DURATION_TYPE: [
    { label: 'Weekly', value: 'week' },
    { label: 'Monthly', value: 'month' },
  ],
  WAGE_TYPE: [
    { label: 'Fixed', value: 'fixed' },
    { label: 'Hourly', value: 'hours' },
  ],
  Leave_TYPE: [
    { label: 'Hours', value: 'Hours' },
    { label: 'Days', value: 'Days' },
  ],
  WAGE_AMOUNT_TYPE: [
    { label: 'Annum', value: 'annum' },
    { label: 'Quarterly (3 months)', value: 'quarterly' },
    { label: 'Month', value: 'month' },
    { label: 'Week', value: 'week' },
    { label: 'Day', value: 'day' },
  ],
  EXPIRY_DATE_DURATION: [
    { label: '2 Month', value: '2_month' },
    { label: '4 Month', value: '4_month' },
    { label: '6 Month', value: '6_month' },
    { label: '1 Year', value: '1_year' },
    { label: 'Custom', value: 'custom' },
  ],
  PLAN_STATUS: [
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'In-Active' },
    // { value: 'disabled', label: 'Disabled' }
  ],
  PROVIDER_STATUS: [
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'In-Active' },
    { value: 'deleted', label: 'Deleted' },
  ],
  PLAN_VISIBILITY_STATUS: [
    { value: 'public', label: 'Public' },
    { value: 'private', label: 'Private' },
  ],
  WORKING_OPTIONS: [
    { label: 'Working', value: 'working' },
    { label: 'Day off', value: 'dayoff' },
  ],
  ORG_STATUS: [
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'In-Active' },
  ],
  HOLIDAY_STATUS: [
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'In-Active' },
  ],
  LEAVE_TYPES: [
    {
      className: 'pending-leave',
      colorClass: 'yellow-dot-wrap',
      label: 'Pending',
    },
    {
      className: 'approved-leave',
      colorClass: 'green-dot-wrap',
      label: 'Approved',
    },
    {
      className: 'rejected-leave',
      colorClass: 'red-dot-wrap',
      label: 'Rejected',
    },
    {
      className: 'cancelled-leave',
      colorClass: 'black-dot-wrap',
      label: 'Cancelled',
    },
    {
      className: 'calendar-holiday',
      colorClass: 'blue-dot-wrap',
      label: 'Holiday',
    },
  ],
  ASSIGN_EMP_STATUS: [
    { value: '', label: 'All' },
    { value: 'pending', label: 'Pending' },
    { value: 'active', label: 'Active' },
    { value: 'ongoing', label: 'Ongoing' },
    { value: 'completed', label: 'Completed' },
    { value: 'verified', label: 'Verified' },
    { value: 'deleted', label: 'Deleted' },
    { value: 'rejected', label: 'Rejected' },
    { value: 'cancelled', label: 'Cancelled' },
  ],
  FORECAST_OPTIONS: [
    { value: '', label: 'All' },
    { value: 'active', label: 'Pending' },
    { value: 'approved', label: 'Approved' },
  ],
  SUBSCRIPTION_TYPE: [
    { label: 'Subscription', value: 'recursive' },
    { label: 'Add on', value: 'one_time' },
  ],
  SUBSCRIPTION_CATEGORY: [
    { label: 'Core', value: 'core' },
    { label: 'Storage', value: 'storage' },
  ],
  YES_NO_OPTIONS: [
    { label: 'Yes', value: 'yes' },
    { label: 'No', value: 'no' },
  ],
  DAY_MONTH_OPTIONS: [
    { label: 'Days', value: 'days' },
    { label: 'Months', value: 'months' },
  ],
  JOINING_OPTIONS: [
    { label: 'Date of Joining', value: 'date_of_joining' },
    { label: 'After Internship End', value: 'after_internship_end' },
    { label: 'After Probation End', value: 'after_probation_end' },
  ],
  TIME_PERIOD_OPTIONS: [
    {
      label: 'Yearly',
      value: 'yearly',
    },
    {
      label: 'Monthly',
      value: 'monthly',
    },
    {
      label: 'Quarterly',
      value: 'quarterly',
    },
    { label: 'Half-Yearly', value: 'half_yearly' },
    { label: 'One-Time', value: 'one_time' },
  ],
  CARRY_FORWARD_OPTIONS: [
    { label: 'Percentage', value: 'percentage' },
    { label: 'Unit', value: 'unit' },
  ],
  ENCASHMENT_PERIOD_OPTIONS: [
    { label: 'Years', value: 'year' },
    { label: 'Months', value: 'month' },
    { label: 'Week', value: 'week' },
  ],
  LEAVE_DEDUCTION_OPTIONS: [
    { label: 'Percentage', value: 'percentage' },
    { label: 'Unit', value: 'unit' },
    { label: 'Maintaining Leave Days', value: 'maintaing_leave_days' },
  ],
  INGREDIENT_TYPE: [
    { value: 'default', label: 'Default' },
    { value: 'custom', label: 'Custom' },
  ],

  PUBLIC_CTA_OPTIONS: [
    {
      label: 'Contact Form',
      value: 'contact_form',
      description: 'Show a basic contact form (Name, Email, Phone, Message)',
    },
    {
      label: 'Contact Info',
      value: 'contact_info',
      description: 'Display a predefined contact block (Phone, Email, Link)',
    },
    {
      label: 'Custom CTA Link',
      value: 'custom_link',
      description: 'Show a custom CTA with text and an external link',
    },
    {
      label: 'None',
      value: 'none',
      description: 'Show nothing',
    },
  ],

  PUBLIC_DISPLAY_FIELDS: [
    {
      id: 1,
      name: 'Category',
      slug: 'category',
      isEnabled: false,
    },
    {
      id: 2,
      name: 'Total Time',
      slug: 'total-time',
      isEnabled: false,
    },
    {
      id: 3,
      name: 'Media',
      slug: 'media',
      isEnabled: false,
    },
    {
      id: 4,
      name: 'Links',
      slug: 'links',
      isEnabled: false,
    },
    {
      id: 5,
      name: 'Ingredients',
      slug: 'ingredients',
      isEnabled: false,
    },
    {
      id: 6,
      name: 'Yield & Portioning',
      slug: 'yield-portioning',
      isEnabled: false,
    },
    {
      id: 7,
      name: 'Cost',
      slug: 'cost',
      isEnabled: false,
    },
    {
      id: 8,
      name: 'Scale',
      slug: 'scale',
      isEnabled: false,
    },
    {
      id: 9,
      name: 'Nutritional Information',
      slug: 'nutritional-information',
      isEnabled: false,
    },
    {
      id: 10,
      name: 'Allergen Information',
      slug: 'allergen-information',
      isEnabled: false,
    },
    {
      id: 11,
      name: 'Dietary Suitability',
      slug: 'dietary-suitability',
      isEnabled: false,
    },
    // {
    //   id: 12,
    //   name: 'Cuisine Type',
    //   slug: 'cuisine-type',
    //   isEnabled: false,
    // },
    {
      id: 13,
      name: 'Serve In',
      slug: 'serve-in',
      isEnabled: false,
    },
    {
      id: 14,
      name: 'Garnish',
      slug: 'garnish',
      isEnabled: false,
    },
    {
      id: 15,
      name: 'Preparation Steps',
      slug: 'preparation-steps',
      isEnabled: false,
    },
    {
      id: 16,
      name: 'HACCP',
      slug: 'haccp',
      isEnabled: false,
    },
  ],

  RECIPE_CATEGORY_OPTIONS: [
    { value: 'Vegetables', label: 'Vegetables' },
    { value: 'Fruits', label: 'Fruits' },
    { value: 'Herbs & Spices', label: 'Herbs & Spices' },
    { value: 'Oils & Fats', label: 'Oils & Fats' },
    { value: 'Dairy', label: 'Dairy' },
    { value: 'Meat & Poultry', label: 'Meat & Poultry' },
    { value: 'Seafood', label: 'Seafood' },
    { value: 'Grains & Cereals', label: 'Grains & Cereals' },
    { value: 'Legumes', label: 'Legumes' },
    { value: 'Nuts & Seeds', label: 'Nuts & Seeds' },
    { value: 'Beverages', label: 'Beverages' },
    { value: 'Condiments', label: 'Condiments' },
    { value: 'Baking Ingredients', label: 'Baking Ingredients' },
  ],

  // Static allergen options
  RECIPE_ALLERGEN_OPTIONS: [
    { value: 'None', label: 'None' },
    { value: 'Gluten', label: 'Gluten' },
    { value: 'Dairy', label: 'Dairy' },
    { value: 'Eggs', label: 'Eggs' },
    { value: 'Nuts', label: 'Nuts' },
    { value: 'Peanuts', label: 'Peanuts' },
    { value: 'Tree Nuts', label: 'Tree Nuts' },
    { value: 'Soy', label: 'Soy' },
    { value: 'Fish', label: 'Fish' },
    { value: 'Shellfish', label: 'Shellfish' },
    { value: 'Crustaceans', label: 'Crustaceans' },
    { value: 'Sesame', label: 'Sesame' },
    { value: 'Sulphites', label: 'Sulphites' },
  ],

  // Static dietary suitability options
  RECIPE_DIETARY_OPTIONS: [
    { value: 'Vegan', label: 'Vegan' },
    { value: 'Vegetarian', label: 'Vegetarian' },
    { value: 'Gluten-Free', label: 'Gluten-Free' },
    { value: 'Dairy-Free', label: 'Dairy-Free' },
    { value: 'Nut-Free', label: 'Nut-Free' },
    { value: 'Soy-Free', label: 'Soy-Free' },
    { value: 'Keto', label: 'Keto' },
    { value: 'Paleo', label: 'Paleo' },
    { value: 'Low-Carb', label: 'Low-Carb' },
    { value: 'High-Protein', label: 'High-Protein' },
    { value: 'Organic', label: 'Organic' },
    { value: 'Non-GMO', label: 'Non-GMO' },
  ],

  NUTRITION_OPTIONS: [
    { label: 'g', value: 'g', id: 1 },
    { label: 'kCal', value: 'kcal', id: 2 },
    { label: 'kJ', value: 'kj', id: 3 },
  ],
  RECIPE_DIFFICULTY_OPTIONS: [
    { value: 'low', label: 'Low' },
    { value: 'medium', label: 'Medium' },
    { value: 'hard', label: 'Hard' },
  ],
  RECIPE_COOKING_TIME_OPTIONS: [
    { value: 'under_30_min', label: 'Under 30 min' },
    { value: '30_60_min', label: '30-60 min' },
    { value: '1_2_hours', label: '1-2 hours' },
    { value: '2+_hours', label: '2+ hours' },
  ],
  RECIPE_OWNERSHIP_OPTIONS: [{ value: 'bookmarked', label: 'Bookmarked' }],
  RECIPE_COMPLEXITY_OPTIONS: [
    { value: 'low', label: 'Low' },
    { value: 'medium', label: 'Medium' },
    { value: 'hard', label: 'High' },
  ],

  // CTA Analytics Filter Options
  CTA_ANALYTICS_TYPE_OPTIONS: [
    { value: 'contact_form', label: 'Contact Form' },
    { value: 'contact_info', label: 'Contact Info' },
    { value: 'custom_cta', label: 'Custom CTA' },
  ],
  YIELD_UNITS: [
    { label: 'Servings', value: 'servings' },
    { label: 'Portions', value: 'portions' },
    { label: 'Cups', value: 'cups' },
    { label: 'Liters', value: 'liters' },
    { label: 'Pieces', value: 'pieces' },
    { label: 'Slices', value: 'slices' },
    { label: 'Bowls', value: 'bowls' },
    { label: 'Plates', value: 'plates' },
    { label: 'Grams', value: 'grams' },
    { label: 'Millilitres', value: 'millilitres' },
    { label: 'Scoops', value: 'scoops' },
  ],
  SERVING_METHODS_OPTIONS: [
    { label: 'Individual plates', value: 'individual_plates' },
    { label: 'Family style', value: 'family_style' },
    { label: 'Buffet style', value: 'buffet_style' },
    { label: 'Shared platters', value: 'shared_platters' },
    { label: 'Tasting portions', value: 'tasting_portions' },
    { label: 'Cocktail style', value: 'cocktail_style' },
    { label: 'Picnic style', value: 'picnic_style' },
  ],
  SERVE_IN_OPTIONS: [
    { label: 'Dinner plates', value: 'dinner_plates' },
    { label: 'Salad plates', value: 'salad_plates' },
    { label: 'Bowls', value: 'bowls' },
    { label: 'Soup bowls', value: 'soup_bowls' },
    { label: 'Ramekins', value: 'ramekins' },
    { label: 'Glasses', value: 'glasses' },
    { label: 'Mugs', value: 'mugs' },
    { label: 'Platters', value: 'platters' },
    { label: 'Serving dishes', value: 'serving_dishes' },
  ],
  ANALYTICS_DATE_RANGES: [
    {
      value: 'last_7_days',
      label: 'Last 7 days',
      description: 'Recent week activity',
    },
    {
      value: 'last_30_days',
      label: 'Last 30 days',
      description: 'Monthly overview',
    },
    {
      value: 'last_90_days',
      label: 'Last 90 days',
      description: 'Quarterly analysis',
    },
    {
      value: 'current_year',
      label: 'Current year',
      description: 'Current performance',
    },
    {
      value: 'last_year',
      label: 'Last year',
      description: 'Annual performance',
    },
    // {
    //   value: 'custom',
    //   label: 'Custom range',
    //   description: 'Select specific dates',
    // },
  ],

  // Support Ticket Filter Options
  SUPPORT_TICKET_STATUS_OPTIONS: [
    { label: 'Open', value: 'open' },
    { label: 'Assigned', value: 'assigned' },
    { label: 'In Progress', value: 'in_progress' },
    { label: 'On Hold', value: 'on_hold' },
    // { label: 'Escalated', value: 'escalated' },
    // { label: 'QA Review', value: 'qa_review' },
    // { label: 'Under Review', value: 'under_review' },
    { label: 'Resolved', value: 'resolved' },
    { label: 'Closed', value: 'closed' },
  ],

  SUPPORT_TICKET_PRIORITY_OPTIONS: [
    { label: 'Low', value: 'low' },
    { label: 'Medium', value: 'medium' },
    { label: 'High', value: 'high' },
    { label: 'Urgent', value: 'urgent' },
  ],

  SUPPORT_TICKET_MODULE_OPTIONS: [
    { label: 'HRMS', value: 'hrms' },
    { label: 'PMS', value: 'pms' },
    { label: 'Other', value: 'other' },
  ],

  SUPPORT_TICKET_TYPE_OPTIONS: [
    { label: 'Bug Report', value: 'bug_report' },
    { label: 'Feature Request', value: 'feature_request' },
    { label: 'General Inquiry', value: 'general_inquiry' },
    { label: 'Export Help', value: 'export_help' },
    { label: 'Technical Issue', value: 'technical_issue' },
    { label: 'Non-Technical Issue', value: 'non_technical_issue' },
    { label: 'Account Issue', value: 'account_issue' },
    { label: 'Billing', value: 'billing' },
    { label: 'Performance', value: 'performance' },
    { label: 'Notifications', value: 'notifications' },
    { label: 'Support', value: 'support' },
  ],
};
