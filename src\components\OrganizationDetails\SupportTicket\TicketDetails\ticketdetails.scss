.details-wrap {
  border-radius: var(--border-radius-md);
  box-shadow: 0 4px 12px rgba(19, 94, 150, 0.15);
  padding: var(--spacing-xxl);
  background-color: var(--color-white);
  border: 1px solid rgba(19, 94, 150, 0.1);

  .ticket-details-wrap {
    margin-top: 0;
    padding-top: 0;

    // Remove any spacing from first child and all children
    > *:first-child {
      margin-top: 0 !important;
      padding-top: 0 !important;
    }

    // Remove spacing from all direct children
    > * {
      &:first-child {
        margin-top: 0 !important;
        padding-top: 0 !important;
      }
    }

    // Target specific MUI Box components
    .MuiBox-root {
      &:first-child {
        margin-top: 0 !important;
        padding-top: 0 !important;
      }
    }

    .ticket-name {
      font-weight: var(--font-weight-medium);
      font-family: var(--font-family-primary);
      font-size: var(--font-size-lg);
      line-height: var(--line-height-base);
      color: var(--text-color-black);

      @media (max-width: 575px) {
        font-size: var(--font-size-lg);
      }
    }
    .org-name-wraper {
      color: var(--text-color-primary);
      font-weight: 600;
    }
    .id-wrap {
      width: max-content;
    }

    .detail-wrap {
      display: flex;
      align-items: center;

      .name-wrap,
      .time-wrap {
        color: var(--color-dark-50);
        font-family: var(--font-family-primary);
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-regular);
        line-height: 1;
      }

      // Very specific targeting - only for user name, from text, org name, and time in ticket details
      .MuiTypography-root {
        &.body-sm.pr4,
        &.from-text.body-sm.pr4,
        &.body-sm:not(.pr4):not(.from-text),
        &.date-time-text.body-sm {
          line-height: 1.2 !important;
          display: flex;
          align-items: center;
        }
      }

      .timer-icon {
        width: var(--icon-size-xsm);
        height: var(--icon-size-xsm);
        fill: var(--icon-color-primary);
      }

      .user-name-text {
        color: var(--color-dark);
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-medium);
        line-height: 1.2;
        display: flex;
        align-items: center;
      }

      .from-text {
        color: var(--color-dark-50);
        font-size: var(--font-size-sm);
        line-height: 1.2;
        display: flex;
        align-items: center;
      }

      .org-name-text {
        color: var(--color-dark);
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-medium);
        line-height: 1.2;
        display: flex;
        align-items: center;
      }

      .date-time-text {
        color: var(--color-dark-50);
        font-size: var(--font-size-sm);
        line-height: 1.2;
        display: flex;
        align-items: center;
      }

      .user-info-section {
        display: flex;
        align-items: center;
        gap: 2px;
        .MuiTypography-root {
          line-height: 1.2 !important;
          display: flex;
          align-items: center;
        }

        .MuiBox-root {
          display: flex;
          align-items: center;
        }
      }

      .time-info-section {
        display: flex;
        align-items: center;
        gap: 2px;
        .MuiTypography-root {
          line-height: 1.2 !important;
          display: flex;
          align-items: center;
        }
      }

      .user-icon {
        font-size: var(--font-size-md);
        color: var(--color-dark-50);
        display: flex;
        align-items: center;
        flex-shrink: 0;
      }

      .time-icon {
        font-size: var(--font-size-md);
        color: var(--color-dark-50);
        display: flex;
        align-items: center;
        flex-shrink: 0;
      }

      .name-wrap {
        max-width: max-content;
        cursor: pointer;
        font-family: var(--font-family-primary);
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-regular);
        line-height: var(--line-height-base);
        color: var(--color-dark-50);

        @media (max-width: 575px) {
          max-width: 100%;
          font-size: var(--font-size-sm);
        }
      }

      @media (max-width: 575px) {
        flex-direction: column;
        align-items: flex-start !important;
        gap: var(--spacing-sm);
      }
    }
  }

  .ticket-tab-handler {
    padding-top: var(--spacing-sm);

    .tabs-wrap {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: var(--normal-sec-border);

      .report-tabs {
        display: flex;
        align-items: center;
        justify-content: space-between;
        overflow: auto;

        .tab-list-sec {
          .tab-name {
            text-transform: none;
            font-size: var(--font-size-sm);
            line-height: var(--line-height-xs);
            font-weight: var(--font-weight-regular);
            color: var(--text-color-black);
            opacity: 1;
            width: max-content;
            padding: var(--spacing-xs);
            font-family: var(--font-family-primary);

            @media (max-width: 1200px) {
              width: max-content;
            }
          }

          .MuiTabs-flexContainer {
            align-items: center;
          }

          .MuiTabs-indicator {
            background-color: var(--icon-color-primary);
            height: var(--border-width-md);
          }

          .Mui-selected {
            color: var(--text-color-primary) !important;
          }

          .MuiTabs-scroller {
            display: block !important;
          }

          .MuiTabScrollButton-root {
            display: block !important;

            .MuiSvgIcon-root {
              margin: var(--spacing-sm);
            }
          }
        }
      }
    }
  }

  @media (max-width: 1199px) {
    margin-right: var(--spacing-xxl);
  }
  @media (max-width: 575px) {
    padding: var(--spacing-sm) var(--spacing-sm) var(--spacing-sm)
      var(--spacing-sm);
    gap: var(--spacing-xxs);
  }
}
