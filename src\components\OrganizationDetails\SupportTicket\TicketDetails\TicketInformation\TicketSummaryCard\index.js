import React from 'react';
import { Box, Typography } from '@mui/material';
import { ListAlt } from '@mui/icons-material';
import './ticketSummaryCard.scss';

export default function TicketSummaryCard({
  ticket,
  getDisplayValue,
  classificationOptions,
  priorityOptions,
  statusOptions,
}) {
  // Function to get status chip class
  const getStatusChipClass = (status) => {
    const statusMap = {
      open: 'draft',
      escalated: 'failed',
      in_progress: 'ongoing',
      on_hold: 'ongoing',
      qa_review: 'probation',
      assigned: 'probation',
      under_review: 'probation',
      resolved: 'success',
      closed: 'status-verified',
    };
    return statusMap[status] || 'draft';
  };

  // Function to get priority chip class
  const getPriorityChipClass = (priority) => {
    const priorityMap = {
      urgent: 'failed', // Red - Most critical
      high: 'high', // Orange/Yellow - High priority but less than urgent
      medium: 'ongoing', // Blue - Medium priority
      low: 'draft', // Green - Low priority
      none: 'draft', // Blue - Default
    };
    return priorityMap[priority] || 'draft';
  };

  return (
    <Box className="ticket-summary-card">
      <Box className="ticket-card-container">
        {/* Header with title and status - Row 1 */}
        <Box className="ticket-header-top">
          <Box className="d-flex align-center gap-sm flex-wrap">
            <Typography className="body-text fw600">#{ticket?.id}</Typography>
            <Typography className="body-text fw600">
              {ticket?.ticket_title}
            </Typography>
            {ticket?.ticket_status && (
              <span
                className={`${getStatusChipClass(ticket?.ticket_status)} sub-title-text fw500`}
              >
                {getDisplayValue(ticket?.ticket_status, statusOptions)}
              </span>
            )}
            {ticket?.ticket_priority && (
              <span
                className={`${getPriorityChipClass(ticket.ticket_priority)} caption-text fw600`}
              >
                {getDisplayValue(
                  ticket?.ticket_priority,
                  priorityOptions
                )?.toUpperCase()}
              </span>
            )}
          </Box>
        </Box>

        {/* Category info with icon - Row 3 */}
        <Box className="ticket-category-info gap-5 pb4">
          <ListAlt className="ticket-category-icon" />
          <Typography className="ticket-category-text sub-title-text">
            {ticket?.ticket_module
              ? getDisplayValue(ticket?.ticket_module, classificationOptions)
              : 'Account Issue'}
          </Typography>
        </Box>

        {/* Assigned info - Row 4 */}
        {ticket?.assigned_full_name && ticket?.isAgentOrAdmin && (
          <Box className="ticket-assigned-section pb4">
            <Typography className="ticket-assigned-text sub-title-text">
              Assigned to :
              <span className="ticket-assigned-name fw500 text-capital">
                {ticket?.isAgentOrAdmin
                  ? 'Support team'
                  : ticket?.assigned_full_name}
              </span>
            </Typography>
          </Box>
        )}
        {/* Description - Row 2 */}
        <Typography className="ticket-description body-sm-regular">
          {ticket?.ticket_description ||
            'User unable to access their account after password reset'}
        </Typography>
      </Box>
    </Box>
  );
}
